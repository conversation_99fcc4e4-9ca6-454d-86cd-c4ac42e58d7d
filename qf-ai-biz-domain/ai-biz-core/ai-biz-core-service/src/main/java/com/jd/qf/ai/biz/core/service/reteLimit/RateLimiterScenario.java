package com.jd.qf.ai.biz.core.service.reteLimit;

/**
 * 限流场景常量
 * 定义系统中各种限流场景的标识符
 * <AUTHOR>
 * @description
 * @date 2025/6/5
 */
public class RateLimiterScenario {

    /**
     * AI意图识别限流
     */
    public static final String AI_INTENT = "ai_intent";

    /**
     * AI聊天限流
     */
    public static final String AI_CHAT = "ai_chat";

    /**
     * 用户级别限流
     */
    public static final String USER_LEVEL = "user_level";

    /**
     * 项目级别限流
     */
    public static final String PROJECT_LEVEL = "project_level";

    /**
     * API调用限流
     */
    public static final String API_CALL = "api_call";

    /**
     * 文件上传限流
     */
    public static final String FILE_UPLOAD = "file_upload";

    /**
     * 知识库查询限流
     */
    public static final String KNOWLEDGE_QUERY = "knowledge_query";

    /**
     * 大模型调用限流
     */
    public static final String LLM_CALL = "llm_call";

    /**
     * 安全检查限流
     */
    public static final String SECURITY_CHECK = "security_check";

    /**
     * 质检服务限流
     */
    public static final String INSPECT_SERVICE = "inspect_service";

    private RateLimiterScenario() {
        // 工具类，禁止实例化
    }
}
