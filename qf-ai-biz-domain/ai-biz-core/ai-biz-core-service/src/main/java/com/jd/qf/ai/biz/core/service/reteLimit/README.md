# 动态限流服务使用说明

## 概述

RateLimiterService 是一个基于Redis令牌桶算法的动态限流服务，支持通过laf配置实时调整限流参数，无需重启应用。

## 核心特性

1. **动态配置**: 通过laf配置中心实时调整限流参数
2. **多场景支持**: 支持不同业务场景的独立限流配置
3. **令牌桶算法**: 使用Redis实现的令牌桶算法，支持突发流量
4. **脚本缓存**: 智能缓存Lua脚本，提高执行效率
5. **兼容性**: 保持与旧版本API的兼容性

## 配置说明

### 1. 默认限流配置

在laf配置中心添加 `defaultRateLimiterConfig` 配置：

```json
{
  "rate": 30,
  "capacity": 30,
  "requested": 1,
  "expire": 2
}
```

### 2. 场景限流配置

在laf配置中心添加 `rateLimiterConfig` 配置：

```json
{
  "ai_intent": {
    "rate": 10,
    "capacity": 20,
    "requested": 1,
    "expire": 60
  },
  "ai_chat": {
    "rate": 5,
    "capacity": 10,
    "requested": 1,
    "expire": 60
  },
  "user_level": {
    "rate": 30,
    "capacity": 60,
    "requested": 1,
    "expire": 300
  },
  "project_level": {
    "rate": 100,
    "capacity": 200,
    "requested": 1,
    "expire": 300
  },
  "api_call": {
    "rate": 50,
    "capacity": 100,
    "requested": 1,
    "expire": 300
  },
  "file_upload": {
    "rate": 1,
    "capacity": 5,
    "requested": 12,
    "expire": 3600
  },
  "llm_call": {
    "rate": 2,
    "capacity": 5,
    "requested": 1,
    "expire": 300
  }
}
```

## 参数说明

- **rate**: 每秒产生的令牌数（令牌生成速率）
- **capacity**: 令牌桶容量（最大令牌数）
- **requested**: 每次请求消耗的令牌数
- **expire**: Redis key过期时间（秒）

## 使用方式

### 1. 直接使用RateLimiterService

```java
@Autowired
private RateLimiterService rateLimiterService;

// 方式1：使用场景配置
boolean isLimited = rateLimiterService.isRateLimited("user:123", "ai_intent");

// 方式2：兼容旧接口
boolean isLimited = rateLimiterService.isRateLimited("user:123", 10, 60);
```

### 2. 使用RateLimiterHelper（推荐）

```java
@Autowired
private RateLimiterHelper rateLimiterHelper;

// 检查用户级别限流
boolean isLimited = rateLimiterHelper.checkUserRateLimit("123");

// 检查AI意图识别限流
boolean isLimited = rateLimiterHelper.checkAiIntentRateLimit("custId123");

// 检查API调用限流
boolean isLimited = rateLimiterHelper.checkApiCallRateLimit("appKey", "/api/chat");
```

## 限流场景

系统预定义了以下限流场景：

- `ai_intent`: AI意图识别限流
- `ai_chat`: AI聊天限流
- `user_level`: 用户级别限流
- `project_level`: 项目级别限流
- `api_call`: API调用限流
- `file_upload`: 文件上传限流
- `knowledge_query`: 知识库查询限流
- `llm_call`: 大模型调用限流
- `security_check`: 安全检查限流
- `inspect_service`: 质检服务限流

## 配置示例

### 高频场景配置
```json
{
  "rate": 100,
  "capacity": 200,
  "requested": 1,
  "expire": 300
}
```
- 每秒允许100个请求
- 支持200个突发请求
- 每个请求消耗1个令牌
- 5分钟过期

### 低频场景配置
```json
{
  "rate": 1,
  "capacity": 5,
  "requested": 12,
  "expire": 3600
}
```
- 每12秒允许1个请求（每分钟5个）
- 支持5个突发请求
- 每个请求消耗12个令牌
- 1小时过期

## 监控和调试

### 查看当前配置
```java
@Autowired
private RateLimiterHelper rateLimiterHelper;

// 打印当前配置状态
rateLimiterHelper.printCurrentStatus();

// 获取推荐配置
Map<String, RateLimiterConfig> recommended = rateLimiterHelper.getRecommendedConfigs();
```

### 日志监控
- 限流触发时会输出WARN级别日志
- 配置变更时会输出INFO级别日志
- 脚本加载失败会输出ERROR级别日志

## 注意事项

1. **配置变更**: laf配置变更后会自动生效，无需重启应用
2. **Redis分片**: 确保相关key路由到同一Redis分片
3. **异常处理**: 脚本执行异常时默认不限流，保证系统可用性
4. **性能考虑**: 脚本会被缓存，相同配置不会重复加载

## 迁移指南

从旧版本迁移到新版本：

1. 保持现有代码不变（兼容旧接口）
2. 在laf中添加新的限流配置
3. 逐步替换为新的API调用方式
4. 移除硬编码的限流参数
