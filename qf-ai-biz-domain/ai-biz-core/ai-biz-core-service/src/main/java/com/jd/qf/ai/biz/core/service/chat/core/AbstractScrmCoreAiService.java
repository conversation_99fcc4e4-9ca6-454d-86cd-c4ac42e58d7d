package com.jd.qf.ai.biz.core.service.chat.core;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.biz.common.constants.RedisPrefixConstants;
import com.jd.qf.ai.biz.common.dto.MixText;
import com.jd.qf.ai.biz.common.enums.*;
import com.jd.qf.ai.biz.common.utils.TimeUtils;
import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.core.service.reteLimit.RateLimiterHelper;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.*;
import com.jd.qf.ai.biz.infrastructure.dao.po.*;
import com.jd.qf.ai.biz.infrastructure.rpc.chat.AgentChatRpcService;
import com.jd.qf.ai.server.common.pojo.dto.CommonMessage;
import com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum;
import com.jd.qf.ai.server.common.pojo.enums.SessionStateEnum;
import com.jd.qf.ai.server.common.pojo.enums.dify.RequestTypeEnum;
import com.jd.qf.ai.server.common.pojo.utils.SequenceNoUtils;
import com.jd.qf.ai.server.sdk.request.GeneralIntentReq;
import com.jd.qf.ai.server.sdk.response.GeneralIntentResp;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import com.jdt.open.capability.lock.LockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jd.qf.ai.biz.common.constants.RedisPrefixConstants.INSERT_AI_RECORD;
import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.HISTORY_MESSAGES;

/**
 * AI聊天核心服务抽象类
 * <AUTHOR>
 * @description
 * @date 2025/5/29
 */
@Component
@Slf4j
public abstract class AbstractScrmCoreAiService implements ScrmCoreAiService{

    @Autowired
    protected AiChatRecordMapper aiChatRecordMapper;
    @Autowired
    protected OpenRedisClient openRedisClient;
    @Autowired
    protected AgentChatRpcService agentChatRpcService;
    @Autowired
    protected AiKnowQaMapper aiKnowQaMapper;
    @LafValue("agentConfig")
    @JsonConverter(isSupportGeneric = true)
    protected Map<String, Map<String, Object>> agentConfigMap;
    @LafValue("contextQueryConfig")
    @JsonConverter(isSupportGeneric = true)
    protected Map<String, Object> contextQueryConfig;
    @Autowired
    protected ProjectMapper projectMapper;
    @Autowired
    protected CustMapper custMapper;
    @Autowired
    protected SysUserMapper sysUserMapper;
    @Autowired
    protected CsChatRecordNewMapper csChatRecordNewMapper;
    @Autowired
    protected LockService lockService;
    @Autowired
    protected RateLimiterHelper rateLimiterHelper;


    @Override
    public Mono<IntentBo> commonIntent(IntentQuery query) {

        String msgRecordId = query.getMsgRecordId();
        //如果是前端请求的意图识别,则在Redis中打个标,标识这条消息前端请求过,后续MQ再请求的时候,便认为处理过了,不再处理
        if (RequestTypeEnum.FRONTEND.getCode().equals(query.getRequestType())) {
            openRedisClient.setEx(RedisPrefixConstants.FRONTEND_REQUEST + msgRecordId, DateUtil.formatDateTime(new Date()), 5);
        }

        // 限流检查
        IntentBo limitResult = performRateLimitCheck(query);
        if (limitResult != null) {
            return Mono.just(limitResult);
        }

        //查询项目下是否配置过AI能力,如果没有配置过,则直接返回不回复,且不记录AI回复
        int res = aiKnowQaMapper.countByProjectId(query.getProjectId());
        if (!agentConfigMap.containsKey(query.getProjectId()) && res == 0) {
            log.warn("项目未配置AI能力,不回复消息,项目ID:{}", query.getProjectId());
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }

        //查询会话基础信息
        ConversationBaseInfo baseInfo = getBaseInfo(query);
        if (baseInfo == null) {
            log.error("未找到会话基础信息,项目ID:{},员工ID:{},客户ID:{}", query.getProjectId(), query.getSysUserId(), query.getCustId());
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }

        //找到查询上下文的配置
        int messageContextTimeLimit = (int) contextQueryConfig.get("messageContextTimeLimit");
        int messageContextLimit = (int) contextQueryConfig.get("messageContextLimit");

        //查询上文
        List<CommonMessage> contextDtoList = queryContextList(query, messageContextTimeLimit, messageContextLimit);

        //先同步地插入一条初始化的记录,根据msgId幂等,后续乐观锁更新
        //加锁,防止多个窗口下的并发请求
        String lockKey = INSERT_AI_RECORD + msgRecordId;
        boolean lock = lockService.lock(lockKey, DateUtil.formatDate(query.getMsgTime()), 30 * 1000);
        if (!lock) {
            log.warn("意图识别初始化AI回复,获取锁失败,说明已有其他工作台窗口在处理,消息ID:{}", msgRecordId);
        } else {
            //加锁成功,保证一个msgId仅插入一条记录
            AiChatRecordPo oldRecord = aiChatRecordMapper.selectByMsgRecordId(msgRecordId);
            if (oldRecord == null) {
                AiChatRecordPo insertPo = buildInitPo(query, contextDtoList, baseInfo);
                log.info("意图识别前,插入一条初始化的记录 - insertPo:{}", JSON.toJSONString(insertPo));
                aiChatRecordMapper.insert(insertPo);
            }
        }

        GeneralIntentReq intentReq = new GeneralIntentReq();
        intentReq.setMessageList(contextDtoList);
        Map<String, Object> params = buildIntentParams(query, baseInfo);
        intentReq.setParams(params);
        return agentChatRpcService.generalIntent(intentReq)
                .map(resp -> processIntentResult(resp, msgRecordId, contextDtoList))
                .timeout(Duration.ofSeconds(10))
                .doFinally(signal -> lockService.unlock(lockKey, DateUtil.formatDate(query.getMsgTime())))
                .onErrorResume(e -> {
                    log.error("意图识别发生异常,直接返回不回复", e);
                    updateFail(msgRecordId);
                    return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
                });
    }

    /**
     * 构建意图识别参数 - 策略方法，由子类实现具体逻辑
     * @param query 意图查询参数
     * @param baseInfo 会话基础信息
     * @return 意图识别参数Map
     */
    protected abstract Map<String, Object> buildIntentParams(IntentQuery query, ConversationBaseInfo baseInfo);

    /**
     * 查询会话基础信息
     */
    private ConversationBaseInfo getBaseInfo(IntentQuery query) {

        ConversationBaseInfo baseInfo = new ConversationBaseInfo();

        String custId = query.getCustId();
        String projectId = query.getProjectId();
        Integer sysUserId = query.getSysUserId();

        //区分群聊还是私聊的场景获取key
        String baseInfoKey = getBaseInfoKey(query);
        String baseInfoValue = openRedisClient.get(baseInfoKey);
        if (StrUtil.isNotBlank(baseInfoValue)) {
            baseInfo = JSON.parseObject(baseInfoValue, ConversationBaseInfo.class);
        } else {
            ProjectPo projectPo = projectMapper.queryByProjectId(projectId);
            if (projectPo == null) {
                log.error("未找到项目信息,项目ID:{}", projectId);
                return null;
            }
            baseInfo.setProjectName(projectPo.getProjectName());
            SysUserPo sysUserPo = sysUserMapper.querySysUserById(sysUserId);
            if (sysUserPo == null) {
                log.error("未找到员工信息,员工ID:{}", sysUserId);
                return null;
            }
            baseInfo.setSysUserName(sysUserPo.getSysUserName());

            //区分群还是私聊的场景查询用户信息
            CustPo custPo=getCustPo(query);
            if (custPo == null) {
                log.error("未找到客户信息,客户ID:{}", custId);
                return null;
            }
            baseInfo.setCustCreateTime(custPo.getCustCreateTime());
            baseInfo.setCustName(custPo.getCustName());
            openRedisClient.setEx(baseInfoKey, JSON.toJSONString(baseInfo), 60 * 60 * 24);
        }
        return baseInfo;
    }

    protected abstract String getBaseInfoKey(IntentQuery query);

    protected abstract CustPo getCustPo(IntentQuery query);

    private List<CommonMessage> queryContextList(IntentQuery query, int messageContextTimeLimit, int messageContextLimit) {

        List<CommonMessage> contextDtoList;
        CsChatRecordQueryPo queryPo = new CsChatRecordQueryPo();
        queryPo.setCorpId(query.getCorpId());
        queryPo.setSysUserId(query.getSysUserId());
        queryPo.setCustId(query.getCustId());
        queryPo.setStartMsgTime(DateUtil.offsetDay(query.getMsgTime(), -messageContextTimeLimit));
        queryPo.setEndMsgTime(query.getMsgTime());
        queryPo.setLimit(messageContextLimit);
        queryPo.setMsgTypeList(List.of(WxMsgTypeEnum.TEXT.getWxMsgType(),WxMsgTypeEnum.MIXTEXT.getWxMsgType()));
        queryPo.setGroupId(query.getGroupId());
        queryPo.setFansType(query.getFansType());
        log.info("上文查询入参: {}", JSON.toJSONString(queryPo));
        List<CsChatRecordPo> csChatRecordPos = csChatRecordNewMapper.queryList(queryPo);
        log.info("上文查询结果条数: {}", csChatRecordPos.size());

        //整合上下文信息
        contextDtoList = csChatRecordPos.stream().map(po -> {

            String msgType = po.getMsgType();
            CommonMessage dto = new CommonMessage();
            String msgJson = po.getMsgJson();
            JSONObject jsonObject = JSON.parseObject(msgJson);

            if (WxMsgTypeEnum.MIXTEXT.getWxMsgType().equals(msgType)) {
                String mixTextJson = jsonObject.getString("mixText");
                List<MixText> mixTexts = JSONObject.parseArray(mixTextJson, MixText.class);
                //拼接所有子类型为文本的内容
                String content = mixTexts.stream()
                        .filter(mixText -> mixText.getSubtype() == 0)
                        .map(MixText::getText).collect(Collectors.joining());
                dto.setContent(content);
            }else if (WxMsgTypeEnum.TEXT.getWxMsgType().equals(msgType)) {
                dto.setContent(jsonObject.getString("content"));
            }
            dto.setRole(ChatTypeEnum.getRoleByKey(po.getChatType()));
            return dto;
        }).toList();
        return contextDtoList;
    }

    private  AiChatRecordPo buildInitPo(IntentQuery query, List<CommonMessage> contextDtoList, ConversationBaseInfo baseInfo) {

        //群聊的场景,custId取wxCustId,因为发消息的客户不一定是我们系统的人
        boolean isGroup = FansTypeEnum.GROUP.getCode().equals(query.getFansType());
        String custId = isGroup ? query.getWxCustId() : query.getCustId();

        AiChatRecordPo insertPo = new AiChatRecordPo();
        insertPo.setRecordNo(SequenceNoUtils.getSequenceNo());
        insertPo.setCorpId(query.getCorpId());
        insertPo.setProjectId(query.getProjectId());
        insertPo.setProjectName(baseInfo.getProjectName());
        insertPo.setMsgRecordId(query.getMsgRecordId());
        insertPo.setMsgTime(TimeUtils.addCurrentTimeMillis(query.getMsgTime()));
        insertPo.setSysUserId(query.getSysUserId());
        insertPo.setSysUserName(baseInfo.getSysUserName());
        insertPo.setCustId(custId);
        insertPo.setCustName(baseInfo.getCustName());
        insertPo.setMsgText(query.getContent());
        insertPo.setDataStatus(AiChatDataStatusEnum.INIT.getCode());
        insertPo.setAcceptStatus(AiChatRecordAcceptStatusEnum.NOT_ACCEPTED.getCode());
        insertPo.setMsgContext(JSON.toJSONString(MapUtil.of(HISTORY_MESSAGES, contextDtoList)));
        insertPo.setValid(1);
        insertPo.setCreator(custId);
        insertPo.setModifier(custId);
        insertPo.setCreatedTime(new DateTime());
        insertPo.setModifiedTime(new DateTime());
        insertPo.setGroupId(query.getGroupId());
        insertPo.setFansType(query.getFansType());
        insertPo.setSessionState(SessionStateEnum.IN_PROGRESS.getCode());
        return insertPo;
    }

    private IntentBo processIntentResult( GeneralIntentResp resp, String msgRecordId, List<CommonMessage> contextDtoList) {

        String result = resp.getIntentType();
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setMsgRecordId(msgRecordId);
        updatePo.setOldDataStatus(AiChatDataStatusEnum.INIT.getCode());
        updatePo.setSessionState(resp.getSessionState());
        updatePo.setAgentId(resp.getAgentId());
        updatePo.setInvokeType(resp.getInvokeType());
        if (IntentTypeEnum.REPLY.getCode().equals(result)) {
            //没有命中就将记录状态修改为处理中,给前端返回要回复
            updatePo.setDataStatus(AiChatDataStatusEnum.PROCESSING.getCode());
            log.info("意图识别后,更新记录状态为处理中 - updatePo:{}", JSON.toJSONString(updatePo));
            aiChatRecordMapper.update(updatePo);
            return IntentBo.builder()
                    .intentType(IntentTypeEnum.REPLY.getCode())
                    .messageList(contextDtoList)
                    .agentId(resp.getAgentId())
                    .build();
        }

        //如果是不回复,将记录更新为不回复,且返回不回复
        updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        updatePo.setNoApplyReason(IntentTypeEnum.getMsgByCode(result));
        log.info("意图识别后,更新记录状态为不回复 - updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);
        return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
    }

    private  void updateFail(String msgRecordId) {
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setMsgRecordId(msgRecordId);
        updatePo.setOldDataStatus(AiChatDataStatusEnum.INIT.getCode());
        updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        updatePo.setNoApplyReason("RPC调用异常");
        log.info("RPC调用异常,更新记录状态为不回复 - updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);
    }


    /**
     * 执行限流检查
     * 包括AI意图识别限流、用户级别限流和内容去重检查
     */
    private IntentBo performRateLimitCheck(IntentQuery query) {

        // 1. AI意图识别限流检查
        if (rateLimiterHelper.checkAiIntentRateLimit(query.getCustId())) {
            log.warn("AI意图识别限流触发 - 客户ID: {}", query.getCustId());
            return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
        }

        // 2. 用户级别限流检查
        if (rateLimiterHelper.checkUserRateLimit(query.getCustId())) {
            log.warn("用户级别限流触发 - 客户ID: {}", query.getCustId());
            return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
        }

        // 3. 项目级别限流检查
        if (rateLimiterHelper.checkProjectRateLimit(query.getProjectId())) {
            log.warn("项目级别限流触发 - 项目ID: {}", query.getProjectId());
            return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
        }

        // 4. 传统的次数限流检查（保持兼容性）
        IntentBo timesLimitResult = limitTimesValidate(query);
        if (timesLimitResult != null) {
            return timesLimitResult;
        }

        // 5. 内容去重检查
        IntentBo contentLimitResult = limitContentValidateSync(query);
        if (contentLimitResult != null) {
            return contentLimitResult;
        }

        return null;
    }

    /**
     * 传统的次数限流检查（保持兼容性）
     */
    private IntentBo limitTimesValidate(IntentQuery query) {
        //1分钟，针对一个客户，只回复有效的10条
        String limitKey = RedisPrefixConstants.MESSAGE_REPLY_LIMIT + query.getCustId();
        String limitValue = openRedisClient.get(limitKey);
        if (limitValue != null) {
            int count = Integer.parseInt(limitValue);
            if (count >= 10) {
                log.warn("客户{}在1分钟内发送了{}条消息,不回复消息", query.getCustId(), count);
                return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
            } else {
                openRedisClient.incr(limitKey);
            }
        } else {
            openRedisClient.setEx(limitKey, "1", 60);
        }
        return null;
    }

    /**
     * 内容去重检查（同步版本）
     */
    private IntentBo limitContentValidateSync(IntentQuery query) {
        // 10分钟内3条同样的话去重
        String dedupeKey = RedisPrefixConstants.MESSAGE_REPLY_DEDUPE + query.getCustId() + "_" + SecureUtil.md5(query.getContent());
        String dedupeValue = openRedisClient.get(dedupeKey);
        if (dedupeValue != null) {
            int count = Integer.parseInt(dedupeValue);
            if (count >= 3) {
                log.warn("客户{}在10分钟内发送了{}条相同的消息,不回复消息", query.getCustId(), count);
                return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
            } else {
                openRedisClient.incr(dedupeKey);
            }
        } else {
            openRedisClient.setEx(dedupeKey, "1", 60 * 10);
        }
        return null;
    }

    /**
     * 内容去重检查（异步版本，保持兼容性）
     * @deprecated 建议使用 limitContentValidateSync 方法
     */
    @Deprecated
    private Mono<IntentBo> limitContentValidate(IntentQuery query) {
        IntentBo result = limitContentValidateSync(query);
        return result != null ? Mono.just(result) : Mono.empty();
    }

}
