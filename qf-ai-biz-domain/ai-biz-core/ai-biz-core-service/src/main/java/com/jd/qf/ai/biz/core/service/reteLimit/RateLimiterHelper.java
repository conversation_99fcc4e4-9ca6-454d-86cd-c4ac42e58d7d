package com.jd.qf.ai.biz.core.service.reteLimit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 限流助手类
 * 提供便捷的限流操作和配置管理
 * <AUTHOR>
 * @description
 * @date 2025/6/5
 */
@Slf4j
@Component
public class RateLimiterHelper {

    @Autowired
    private RateLimiterService rateLimiterService;

    /**
     * 检查用户级别限流
     * @param userId 用户ID
     * @return true表示被限流
     */
    public boolean checkUserRateLimit(String userId) {
        String key = "user:" + userId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.USER_LEVEL);
    }

    /**
     * 检查项目级别限流
     * @param projectId 项目ID
     * @return true表示被限流
     */
    public boolean checkProjectRateLimit(String projectId) {
        String key = "project:" + projectId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.PROJECT_LEVEL);
    }

    /**
     * 检查AI意图识别限流
     * @param custId 客户ID
     * @return true表示被限流
     */
    public boolean checkAiIntentRateLimit(String custId) {
        String key = "ai_intent:" + custId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.AI_INTENT);
    }

    /**
     * 检查AI聊天限流
     * @param conversationId 会话ID
     * @return true表示被限流
     */
    public boolean checkAiChatRateLimit(String conversationId) {
        String key = "ai_chat:" + conversationId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.AI_CHAT);
    }

    /**
     * 检查API调用限流
     * @param appKey 应用Key
     * @param apiPath API路径
     * @return true表示被限流
     */
    public boolean checkApiCallRateLimit(String appKey, String apiPath) {
        String key = "api:" + appKey + ":" + apiPath;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.API_CALL);
    }

    /**
     * 检查大模型调用限流
     * @param modelType 模型类型
     * @param userId 用户ID
     * @return true表示被限流
     */
    public boolean checkLlmCallRateLimit(String modelType, String userId) {
        String key = "llm:" + modelType + ":" + userId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.LLM_CALL);
    }

    /**
     * 检查文件上传限流
     * @param userId 用户ID
     * @return true表示被限流
     */
    public boolean checkFileUploadRateLimit(String userId) {
        String key = "file_upload:" + userId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.FILE_UPLOAD);
    }

    /**
     * 检查知识库查询限流
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return true表示被限流
     */
    public boolean checkKnowledgeQueryRateLimit(String projectId, String userId) {
        String key = "knowledge:" + projectId + ":" + userId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.KNOWLEDGE_QUERY);
    }

    /**
     * 检查安全检查限流
     * @param userId 用户ID
     * @return true表示被限流
     */
    public boolean checkSecurityCheckRateLimit(String userId) {
        String key = "security:" + userId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.SECURITY_CHECK);
    }

    /**
     * 检查质检服务限流
     * @param userId 用户ID
     * @return true表示被限流
     */
    public boolean checkInspectServiceRateLimit(String userId) {
        String key = "inspect:" + userId;
        return rateLimiterService.isRateLimited(key, RateLimiterScenario.INSPECT_SERVICE);
    }

    /**
     * 获取推荐的限流配置模板
     * 可以作为laf配置的参考
     */
    public Map<String, RateLimiterConfig> getRecommendedConfigs() {
        Map<String, RateLimiterConfig> configs = new HashMap<>();
        
        // AI意图识别：每秒10个请求，桶容量20
        configs.put(RateLimiterScenario.AI_INTENT, new RateLimiterConfig(10, 20, 1, 60));
        
        // AI聊天：每秒5个请求，桶容量10
        configs.put(RateLimiterScenario.AI_CHAT, new RateLimiterConfig(5, 10, 1, 60));
        
        // 用户级别：每秒30个请求，桶容量60
        configs.put(RateLimiterScenario.USER_LEVEL, new RateLimiterConfig(30, 60, 1, 300));
        
        // 项目级别：每秒100个请求，桶容量200
        configs.put(RateLimiterScenario.PROJECT_LEVEL, new RateLimiterConfig(100, 200, 1, 300));
        
        // API调用：每秒50个请求，桶容量100
        configs.put(RateLimiterScenario.API_CALL, new RateLimiterConfig(50, 100, 1, 300));
        
        // 文件上传：每分钟5个请求（每秒0.083个），桶容量5
        configs.put(RateLimiterScenario.FILE_UPLOAD, new RateLimiterConfig(1, 5, 12, 3600));
        
        // 知识库查询：每秒20个请求，桶容量40
        configs.put(RateLimiterScenario.KNOWLEDGE_QUERY, new RateLimiterConfig(20, 40, 1, 300));
        
        // 大模型调用：每秒2个请求，桶容量5
        configs.put(RateLimiterScenario.LLM_CALL, new RateLimiterConfig(2, 5, 1, 300));
        
        // 安全检查：每秒10个请求，桶容量20
        configs.put(RateLimiterScenario.SECURITY_CHECK, new RateLimiterConfig(10, 20, 1, 300));
        
        // 质检服务：每秒5个请求，桶容量10
        configs.put(RateLimiterScenario.INSPECT_SERVICE, new RateLimiterConfig(5, 10, 1, 300));
        
        return configs;
    }

    /**
     * 打印当前限流配置状态
     */
    public void printCurrentStatus() {
        log.info("=== 当前限流配置状态 ===");
        log.info("默认配置: {}", rateLimiterService.getDefaultConfig());
        
        Map<String, RateLimiterConfig> currentConfigs = rateLimiterService.getCurrentConfig();
        if (currentConfigs.isEmpty()) {
            log.info("场景配置: 无");
        } else {
            log.info("场景配置:");
            currentConfigs.forEach((scenario, config) -> 
                log.info("  {}: {}", scenario, config));
        }
        log.info("========================");
    }
}
