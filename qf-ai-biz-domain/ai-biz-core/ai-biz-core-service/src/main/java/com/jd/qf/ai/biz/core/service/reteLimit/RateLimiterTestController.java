package com.jd.qf.ai.biz.core.service.reteLimit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 限流测试控制器
 * 用于测试和验证限流功能
 * <AUTHOR>
 * @description
 * @date 2025/6/5
 */
@Slf4j
@RestController
@RequestMapping("/test/rate-limiter")
public class RateLimiterTestController {

    @Autowired
    private RateLimiterService rateLimiterService;
    
    @Autowired
    private RateLimiterHelper rateLimiterHelper;

    /**
     * 测试基础限流功能
     */
    @GetMapping("/basic")
    public Map<String, Object> testBasicRateLimit(
            @RequestParam String key,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(defaultValue = "60") Integer timeWindow) {
        
        Map<String, Object> result = new HashMap<>();
        boolean isLimited = rateLimiterService.isRateLimited(key, limit, timeWindow);
        
        result.put("key", key);
        result.put("limit", limit);
        result.put("timeWindow", timeWindow);
        result.put("isLimited", isLimited);
        result.put("message", isLimited ? "请求被限流" : "请求通过");
        result.put("timestamp", System.currentTimeMillis());
        
        log.info("基础限流测试 - key: {}, limit: {}, timeWindow: {}, result: {}", 
                key, limit, timeWindow, isLimited ? "限流" : "通过");
        
        return result;
    }

    /**
     * 测试场景限流功能
     */
    @GetMapping("/scenario")
    public Map<String, Object> testScenarioRateLimit(
            @RequestParam String key,
            @RequestParam String scenario) {
        
        Map<String, Object> result = new HashMap<>();
        boolean isLimited = rateLimiterService.isRateLimited(key, scenario);
        
        result.put("key", key);
        result.put("scenario", scenario);
        result.put("isLimited", isLimited);
        result.put("message", isLimited ? "请求被限流" : "请求通过");
        result.put("timestamp", System.currentTimeMillis());
        
        log.info("场景限流测试 - key: {}, scenario: {}, result: {}", 
                key, scenario, isLimited ? "限流" : "通过");
        
        return result;
    }

    /**
     * 测试AI意图识别限流
     */
    @GetMapping("/ai-intent")
    public Map<String, Object> testAiIntentRateLimit(@RequestParam String custId) {
        Map<String, Object> result = new HashMap<>();
        boolean isLimited = rateLimiterHelper.checkAiIntentRateLimit(custId);
        
        result.put("custId", custId);
        result.put("scenario", "ai_intent");
        result.put("isLimited", isLimited);
        result.put("message", isLimited ? "AI意图识别被限流" : "AI意图识别通过");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 测试用户级别限流
     */
    @GetMapping("/user-level")
    public Map<String, Object> testUserLevelRateLimit(@RequestParam String userId) {
        Map<String, Object> result = new HashMap<>();
        boolean isLimited = rateLimiterHelper.checkUserRateLimit(userId);
        
        result.put("userId", userId);
        result.put("scenario", "user_level");
        result.put("isLimited", isLimited);
        result.put("message", isLimited ? "用户级别被限流" : "用户级别通过");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 测试项目级别限流
     */
    @GetMapping("/project-level")
    public Map<String, Object> testProjectLevelRateLimit(@RequestParam String projectId) {
        Map<String, Object> result = new HashMap<>();
        boolean isLimited = rateLimiterHelper.checkProjectRateLimit(projectId);
        
        result.put("projectId", projectId);
        result.put("scenario", "project_level");
        result.put("isLimited", isLimited);
        result.put("message", isLimited ? "项目级别被限流" : "项目级别通过");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 获取当前限流配置
     */
    @GetMapping("/config")
    public Map<String, Object> getCurrentConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("defaultConfig", rateLimiterService.getDefaultConfig());
        result.put("scenarioConfigs", rateLimiterService.getCurrentConfig());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 打印当前限流状态
     */
    @GetMapping("/status")
    public Map<String, Object> printStatus() {
        rateLimiterHelper.printCurrentStatus();
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "限流状态已打印到日志");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 获取推荐配置
     */
    @GetMapping("/recommended-config")
    public Map<String, Object> getRecommendedConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("recommendedConfigs", rateLimiterHelper.getRecommendedConfigs());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 批量测试限流
     */
    @PostMapping("/batch-test")
    public Map<String, Object> batchTest(
            @RequestParam String keyPrefix,
            @RequestParam String scenario,
            @RequestParam(defaultValue = "10") Integer count) {
        
        Map<String, Object> result = new HashMap<>();
        Map<String, Boolean> testResults = new HashMap<>();
        
        for (int i = 1; i <= count; i++) {
            String key = keyPrefix + "_" + i;
            boolean isLimited = rateLimiterService.isRateLimited(key, scenario);
            testResults.put(key, isLimited);
        }
        
        long limitedCount = testResults.values().stream().mapToLong(limited -> limited ? 1 : 0).sum();
        long passedCount = count - limitedCount;
        
        result.put("keyPrefix", keyPrefix);
        result.put("scenario", scenario);
        result.put("totalCount", count);
        result.put("passedCount", passedCount);
        result.put("limitedCount", limitedCount);
        result.put("testResults", testResults);
        result.put("timestamp", System.currentTimeMillis());
        
        log.info("批量限流测试 - keyPrefix: {}, scenario: {}, total: {}, passed: {}, limited: {}", 
                keyPrefix, scenario, count, passedCount, limitedCount);
        
        return result;
    }
}
