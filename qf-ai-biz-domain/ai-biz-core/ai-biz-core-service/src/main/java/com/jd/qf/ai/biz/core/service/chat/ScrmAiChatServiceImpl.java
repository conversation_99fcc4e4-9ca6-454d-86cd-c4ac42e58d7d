package com.jd.qf.ai.biz.core.service.chat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.biz.common.constants.RedisPrefixConstants;
import com.jd.qf.ai.biz.common.enums.*;
import com.jd.qf.ai.biz.core.api.chat.ScrmAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.core.api.know.qa.KnowledgeBaseService;
import com.jd.qf.ai.biz.core.api.know.qa.bo.KnowledgeBaseIdConfig;
import com.jd.qf.ai.biz.core.converter.Converter;
import com.jd.qf.ai.biz.core.service.chat.core.ScrmCoreAiService;
import com.jd.qf.ai.biz.core.service.chat.core.ScrmCoreAiServiceFactory;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.*;
import com.jd.qf.ai.biz.infrastructure.dao.po.*;
import com.jd.qf.ai.biz.infrastructure.rpc.chat.AgentChatRpcService;
import com.jd.qf.ai.server.common.pojo.enums.*;
import com.jd.qf.ai.server.sdk.request.ChatReq;
import com.jd.qf.ai.server.sdk.response.ChatResp;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import com.jdt.open.capability.log.annotation.OpenLog;
import com.jdt.open.capability.ratelimiter.OpenRateLimit;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.AGENT_ID;

@Service
@Slf4j
public class ScrmAiChatServiceImpl implements ScrmAiChatService {


    @Resource
    AiChatRecordMapper aiChatRecordMapper;

    @LafValue("agentConfig")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, Map<String, Object>> agentConfigMap;
    @Autowired
    private AgentChatRpcService agentChatRpcService;
    @Autowired
    private OpenRedisClient openRedisClient;
    @Autowired
    private KnowledgeBaseService knowledgeBaseService;
    @Autowired
    private ScrmCoreAiServiceFactory scrmCoreAiServiceFactory;
    @Autowired
    private CustMapper custMapper;

    /**
     * 更新采纳状态
     *
     * @param query 包含更新接受状态所需参数的查询对象
     */
    @Override
    @OpenLog("更新采纳状态")
    public void updateAcceptStatus(UpdateAcceptStatusQuery query) {

        AiChatRecordPo aiChatRecordPo = aiChatRecordMapper.selectByRecordNo(query.getRecordNo());
        if (aiChatRecordPo == null) {
            log.warn("ScrmAiChatServiceImpl - updateAcceptStatus - 记录不存在 - query:{}", query);
            throw new BizException("记录不存在");
        }

        //乐观锁更新
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setRecordNo(query.getRecordNo());
        updatePo.setAcceptStatus(query.getAcceptStatus());
        updatePo.setOldAcceptStatus(AiChatRecordAcceptStatusEnum.NOT_ACCEPTED.getCode());
        log.info("更新采纳状态入参{}", JSON.toJSONString(updatePo));
        int update = aiChatRecordMapper.update(updatePo);
        log.info("更新采纳状态结果{}", update);
    }

    @Override
    @OpenLog("分页查询AI聊天记录")
    public List<AiChatRecordBo> pageQueryRecord(AiChatRecordPageQuery query) {
        AiChatRecordQueryPo queryPo = Converter.INSTANCE.to(query);
        queryPo.setExcludeDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        List<AiChatRecordPo> list = aiChatRecordMapper.selectListByLocation(queryPo);
        if (LocationTypeEnum.BEFORE.getCode().equals(query.getLocationType())) {
            //如果是向前翻页,那么需要将列表反转,保证给前端的数据都是按照msgTime由小到大排序的
            list = CollectionUtil.reverse(list);
        }

        List<AiChatRecordBo> boList = Converter.INSTANCE.toAiBoList(list);

        //填充客户头像
        if (CollectionUtil.isNotEmpty(boList)){
            List<String> custIdList = boList.stream().map(AiChatRecordBo::getCustId).toList();
            List<CustPo> custPoList;
            if (StrUtil.isBlank(query.getGroupId())){
                //私聊客户头像从cust表取
                custPoList=custMapper.queryByCustIdList(custIdList);
            }else {
                //群聊客户头像从群关系表取
                custPoList=custMapper.queryByWxUserIdList(query.getGroupId(),custIdList);
            }
            Map<String, String> custAvatarMap = custPoList.stream().collect(Collectors.toMap(CustPo::getCustId, CustPo::getCustAvatar));
            boList.forEach(po -> po.setCustAvatar(custAvatarMap.get(po.getCustId())));
        }

        return boList;
    }

    @Override
    public Mono<IntentBo> commonIntent(IntentQuery query) {
        // 使用工厂类获取对应的处理器，委托给具体的策略实现
        ScrmCoreAiService coreAiService = scrmCoreAiServiceFactory.getService(query.getFansType());
        return coreAiService.commonIntent(query);
    }

    private  void updateFail(String msgRecordId) {
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setMsgRecordId(msgRecordId);
        updatePo.setOldDataStatus(AiChatDataStatusEnum.INIT.getCode());
        updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        updatePo.setNoApplyReason("RPC调用异常");
        log.info("更新记录状态为不回复 - updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);
    }

    @Override
    @OpenLog("异步请求AI回复")
    @OpenRateLimit(key = "com.jd.qf.ai.biz.core.service.chat.ScrmAiChatServiceImpl.asyncReply")
    public void asyncReply(AsyncReplyCommand command) {
        //空实现,兼容线上旧的接口,后续MQ处理方式稳定后可去掉
    }

    @Override
    @OpenLog("流式请求AI回复")
    public Flux<StreamChatBo> streamReply(ReplyCommand command) {

        //判断同一条消息是否已有记录,如果已有记录是COMPLETED状态,则直接返回
        String msgRecordId = command.getMsgRecordId();
        AiChatRecordPo aiChatRecordPo = aiChatRecordMapper.selectByMsgRecordId(msgRecordId);
        if (aiChatRecordPo == null || AiChatDataStatusEnum.INIT.getCode().equals(aiChatRecordPo.getDataStatus())) {
            log.error("未找到AI聊天记录或状态不正确,消息ID:{},记录为:{}", msgRecordId, JSON.toJSONString(aiChatRecordPo));
            return Flux.empty();
        } else if (AiChatDataStatusEnum.COMPLETED.getCode().equals(aiChatRecordPo.getDataStatus())) {
            //已完成的记录直接返回
            log.info("AI聊天记录已存在且已完成,直接返回,记录为:{}", JSON.toJSONString(aiChatRecordPo));
            return justFluxResp(aiChatRecordPo.getRecordNo(), aiChatRecordPo.getAiAnswer());
        }

        //获取agent配置,后续废弃,兼容旧逻辑
        Map<String, Object> map = agentConfigMap.get(aiChatRecordPo.getProjectId());

        //这里兼容下旧逻辑,如果记录中没有agentId,则使用DUCC配置项目的agentId
        //invokeType同理,没有的话就按照流式调用;稳定后可以直接用记录中的
        String agentId = StrUtil.isBlank(aiChatRecordPo.getAgentId()) ? (String) map.get(AGENT_ID) : aiChatRecordPo.getAgentId();
        String invokeType = StrUtil.isBlank(aiChatRecordPo.getInvokeType()) ? InvokeTypeEnum.STREAM.getCode() : aiChatRecordPo.getInvokeType();

        //带着上下文去请求Agent服务
        if (InvokeTypeEnum.STREAM.getCode().equals(invokeType)) {
            return streamChat(command, agentId,aiChatRecordPo);
        }else {
            return blockChat(command, agentId,aiChatRecordPo);
        }
    }

    private Flux<StreamChatBo> blockChat(ReplyCommand command, String agentId,AiChatRecordPo po) {
        ChatReq chatReq = new ChatReq();
        chatReq.setAgentType(AgentTypeEnum.SELF_BUILD.getCode());
        chatReq.setAgentId(agentId);
        chatReq.setAllMessage(command.getMessageList());
        //安全检查用
        chatReq.setQuery(command.getMessageList().get(0).getContent());

        //填充业务参数
        Map<String, Object> params = new HashMap<>();
        params.put("is_group", FansTypeEnum.GROUP.getCode().equals(po.getFansType()));
        KnowledgeBaseIdConfig knowledgeBaseIdConfig = knowledgeBaseService.getAiKnowConfigPos(po.getProjectId());
        if (knowledgeBaseIdConfig==null){
            log.warn("项目下不存在知识库配置,入参{}", JSON.toJSONString(po.getProjectId()));
            return justFluxResp(po.getRecordNo(), StrUtil.EMPTY);
        }
        params.put("question_dataset_id", knowledgeBaseIdConfig.getQuestionId());
        params.put("qa_dataset_id", knowledgeBaseIdConfig.getQaId());
        params.put("agent_id", agentId);

        chatReq.setInputs(params);

        AtomicReference<StringBuilder> fullResponse = new AtomicReference<>(new StringBuilder());
        return agentChatRpcService.chat(chatReq)
                .flatMapMany(resp -> {
                    fullResponse.get().append(resp.getAnswer());
                    return justFluxResp(po.getRecordNo(), resp.getAnswer());
                })
                .timeout(Duration.ofSeconds(10))
                .doOnComplete(() -> this.doOnComplete(command, fullResponse.get().toString(), po.getRecordNo()))
                .onErrorResume(Exception.class, e -> {
                    log.error("AI回复发生未知异常", e);
                    updateFail(po.getMsgRecordId());
                    return Flux.empty();
                });
    }

    private  Flux<StreamChatBo> justFluxResp(String recordNo, String answer) {
        return Flux.just(
                StreamChatBo.builder()
                        .answer(answer)
                        .event(MessageTypeEnum.MESSAGE.getCode())
                        .recordNo(recordNo)
                        .build(),
                StreamChatBo.builder()
                        .event(MessageTypeEnum.MESSAGE_END.getCode())
                        .recordNo(recordNo)
                        .build()
        );
    }

    private Flux<StreamChatBo> streamChat(ReplyCommand command, String agentId, AiChatRecordPo po) {

        String recordNo = po.getRecordNo();

        AtomicReference<StringBuilder> fullResponse = new AtomicReference<>(new StringBuilder());

        ChatReq chatReq = new ChatReq();
        chatReq.setAgentType(AgentTypeEnum.SELF_BUILD.getCode());
        chatReq.setAgentId(agentId);
        chatReq.setAllMessage(command.getMessageList());
        //请求完成后,强制更新记录状态为COMPLETED,覆盖大模型回复
        //在redis中新增AI未读数
        return agentChatRpcService.streamChat(chatReq)
                .timeout(Duration.ofSeconds(20))
                .doOnNext(response -> {
                    if (response != null && response.getAnswer() != null) {
                        fullResponse.get().append(response.getAnswer());
                    }
                })
                .doOnComplete(() -> this.doOnComplete(command, fullResponse.get().toString(), recordNo))
                .map(resp -> {
                    StreamChatBo streamChatBo = Converter.INSTANCE.to(resp);
                    streamChatBo.setRecordNo(recordNo);
                    return streamChatBo;
                }).onErrorResume(e -> {
                    log.error("流式请求AI回复发生异常", e);
                    updateFail(po.getMsgRecordId());
                    return Flux.empty();
                });
    }

    private void doOnComplete(ReplyCommand command, String completeResponse, String recordNo) {
        //请求完成后,强制更新记录状态为COMPLETED,覆盖大模型回复
        log.info("收到完整大模型回复{}", completeResponse);
        boolean nullOrUndefined = StrUtil.isNullOrUndefined(completeResponse);

        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setRecordNo(recordNo);
        updatePo.setAiAnswer(completeResponse);
        if (nullOrUndefined){
            log.info("AI回复为空,更新记录状态为不回复");
            updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        }else {
            log.info("AI回复不为空,更新记录状态为已完成");
            updatePo.setDataStatus(AiChatDataStatusEnum.COMPLETED.getCode());
        }

        log.info("更新AI回复记录最终状态,updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);

        //非空的回复才在redis中新增AI未读数
        if (!nullOrUndefined){
            String unReadKey = getUnReadKey(command.getFansType(), command.getCustId(), command.getGroupId());
            if (StrUtil.isBlank(openRedisClient.get(unReadKey))) {
                openRedisClient.set(unReadKey, "1");
            } else {
                openRedisClient.incr(unReadKey);
            }
        }
    }

    @Override
    public UnReadAiRecordBo queryUnRead(OpUnReadAiRecordCommand req) {
        UnReadAiRecordBo unReadAiRecordBo = new UnReadAiRecordBo();
        String unReadKey = getUnReadKey(req.getFansType(),req.getCustId(),req.getGroupId());
        String unReadValue = openRedisClient.get(unReadKey);
        if (StrUtil.isNotBlank(unReadValue)) {
            unReadAiRecordBo.setCount(Integer.parseInt(unReadValue));
        }
        return unReadAiRecordBo;
    }

    @Override
    public void clearUnRead(OpUnReadAiRecordCommand req) {
        String unReadKey = getUnReadKey(req.getFansType(),req.getCustId(),req.getGroupId());
        openRedisClient.del(unReadKey);
    }

    private  String getUnReadKey(String fansType,String custId,String groupId) {
        String unReadKey = RedisPrefixConstants.AI_UNREAD;
        if (FansTypeEnum.GROUP.getCode().equals(fansType)) {
            unReadKey += groupId;
        } else {
            unReadKey += custId;
        }
        return unReadKey;
    }
}
