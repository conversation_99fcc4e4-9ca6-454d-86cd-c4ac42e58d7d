package com.jd.qf.ai.biz.core.service.reteLimit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 限流配置类
 * 用于配置令牌桶算法的各项参数
 * <AUTHOR>
 * @description
 * @date 2025/6/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RateLimiterConfig {

    /**
     * 每秒产生的令牌数（令牌生成速率）
     * 例如：30表示每秒产生30个令牌
     */
    private int rate = 30;

    /**
     * 令牌桶容量（最大令牌数）
     * 例如：30表示桶最多存储30个令牌
     */
    private int capacity = 30;

    /**
     * 每次请求消耗的令牌数
     * 通常为1，表示每个请求消耗1个令牌
     */
    private int requested = 1;

    /**
     * Redis key过期时间（秒）
     * 建议大于1秒，避免频繁过期
     */
    private int expire = 2;

    /**
     * 生成配置的唯一标识，用于脚本缓存
     */
    public String toConfigKey() {
        return String.format("rate_%d_capacity_%d_requested_%d_expire_%d", 
                rate, capacity, requested, expire);
    }

    /**
     * 验证配置的有效性
     */
    public boolean isValid() {
        return rate > 0 && capacity > 0 && requested > 0 && expire > 0;
    }

    /**
     * 获取每秒允许的请求数
     * 这是一个便捷方法，实际限流能力取决于rate和requested的比值
     */
    public double getRequestsPerSecond() {
        return (double) rate / requested;
    }

    /**
     * 获取突发请求容量
     * 表示在令牌桶满的情况下，能够处理的最大突发请求数
     */
    public int getBurstCapacity() {
        return capacity / requested;
    }

    @Override
    public String toString() {
        return String.format("RateLimiterConfig{rate=%d/s, capacity=%d, requested=%d, expire=%ds, rps=%.1f, burst=%d}", 
                rate, capacity, requested, expire, getRequestsPerSecond(), getBurstCapacity());
    }
}
