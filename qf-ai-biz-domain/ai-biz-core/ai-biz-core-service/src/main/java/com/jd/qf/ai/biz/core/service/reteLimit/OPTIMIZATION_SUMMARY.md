# RateLimiterService 优化总结

## 优化概述

本次优化将原有的硬编码限流服务升级为支持laf动态配置的智能限流系统，实现了真正的动态限流能力。

## 主要改进

### 1. 动态配置支持
- **原有问题**: Lua脚本中的限流参数（rate=30, capacity=30）是硬编码的
- **优化方案**: 通过laf配置中心动态管理限流参数
- **配置项**:
  - `defaultRateLimiterConfig`: 默认限流配置
  - `rateLimiterConfig`: 场景化限流配置映射

### 2. 多场景限流
- **原有问题**: 只有单一的限流逻辑
- **优化方案**: 支持多种业务场景的独立限流配置
- **支持场景**:
  - AI意图识别限流 (`ai_intent`)
  - AI聊天限流 (`ai_chat`)
  - 用户级别限流 (`user_level`)
  - 项目级别限流 (`project_level`)
  - API调用限流 (`api_call`)
  - 文件上传限流 (`file_upload`)
  - 知识库查询限流 (`knowledge_query`)
  - 大模型调用限流 (`llm_call`)
  - 安全检查限流 (`security_check`)
  - 质检服务限流 (`inspect_service`)

### 3. 智能脚本管理
- **原有问题**: 脚本在启动时加载一次，无法动态更新
- **优化方案**: 
  - 脚本SHA缓存机制，避免重复加载
  - 配置变更时自动清空缓存，强制重新加载
  - 动态Lua脚本，参数通过ARGV传递

### 4. 向后兼容性
- **保持兼容**: 原有的 `isRateLimited(key, limit, timeWindow)` 接口继续可用
- **新增接口**: `isRateLimited(key, scenario)` 支持场景化限流
- **平滑迁移**: 可以逐步从旧接口迁移到新接口

### 5. 增强的监控和调试
- **配置监控**: 提供当前配置查看接口
- **状态打印**: 支持打印当前限流状态到日志
- **测试接口**: 提供完整的测试控制器用于验证功能

## 新增文件

### 核心类
1. **RateLimiterConfig.java** - 限流配置类
2. **RateLimiterScenario.java** - 限流场景常量
3. **RateLimiterHelper.java** - 限流助手类
4. **RateLimiterTestController.java** - 测试控制器

### 文档和配置
1. **README.md** - 详细使用说明
2. **laf-config-example.json** - laf配置示例
3. **OPTIMIZATION_SUMMARY.md** - 本优化总结

## 配置示例

### laf配置中心配置

```json
{
  "defaultRateLimiterConfig": {
    "rate": 30,
    "capacity": 30,
    "requested": 1,
    "expire": 2
  },
  "rateLimiterConfig": {
    "ai_intent": {
      "rate": 10,
      "capacity": 20,
      "requested": 1,
      "expire": 60
    },
    "user_level": {
      "rate": 30,
      "capacity": 60,
      "requested": 1,
      "expire": 300
    }
  }
}
```

## 使用方式

### 1. 基础使用（兼容旧版本）
```java
@Autowired
private RateLimiterService rateLimiterService;

boolean isLimited = rateLimiterService.isRateLimited("user:123", 10, 60);
```

### 2. 场景化使用（推荐）
```java
@Autowired
private RateLimiterHelper rateLimiterHelper;

// AI意图识别限流
boolean isLimited = rateLimiterHelper.checkAiIntentRateLimit("custId123");

// 用户级别限流
boolean isLimited = rateLimiterHelper.checkUserRateLimit("userId123");
```

## 集成到现有代码

在 `AbstractScrmCoreAiService` 中已经集成了新的限流逻辑：

```java
// 限流检查
IntentBo limitResult = performRateLimitCheck(query);
if (limitResult != null) {
    return Mono.just(limitResult);
}
```

限流检查包括：
1. AI意图识别限流
2. 用户级别限流  
3. 项目级别限流
4. 传统次数限流（兼容性）
5. 内容去重检查

## 测试验证

### 测试接口
- `GET /test/rate-limiter/basic` - 基础限流测试
- `GET /test/rate-limiter/scenario` - 场景限流测试
- `GET /test/rate-limiter/ai-intent` - AI意图识别限流测试
- `GET /test/rate-limiter/config` - 查看当前配置
- `POST /test/rate-limiter/batch-test` - 批量测试

### 测试示例
```bash
# 测试AI意图识别限流
curl "http://localhost:8080/test/rate-limiter/ai-intent?custId=test123"

# 查看当前配置
curl "http://localhost:8080/test/rate-limiter/config"

# 批量测试
curl -X POST "http://localhost:8080/test/rate-limiter/batch-test?keyPrefix=test&scenario=ai_intent&count=20"
```

## 监控和运维

### 日志监控
- 限流触发时输出WARN级别日志
- 配置变更时输出INFO级别日志
- 脚本加载失败输出ERROR级别日志

### 配置管理
- 通过laf配置中心实时调整限流参数
- 配置变更后自动生效，无需重启应用
- 支持回滚到默认配置

## 性能优化

1. **脚本缓存**: 相同配置的脚本只加载一次
2. **异常处理**: 脚本执行异常时默认不限流，保证系统可用性
3. **Redis优化**: 使用HSET存储令牌状态，减少Redis操作次数

## 后续扩展

1. **指标监控**: 可以集成Prometheus指标，监控限流效果
2. **动态调整**: 可以根据系统负载自动调整限流参数
3. **分布式限流**: 可以扩展为跨实例的分布式限流
4. **限流策略**: 可以支持更多限流算法（滑动窗口、漏桶等）

## 注意事项

1. **Redis分片**: 确保相关key路由到同一Redis分片
2. **配置格式**: laf配置必须符合RateLimiterConfig的JSON格式
3. **向后兼容**: 旧的限流逻辑仍然保留，确保平滑迁移
4. **异常处理**: 限流服务异常时不影响业务正常流程
