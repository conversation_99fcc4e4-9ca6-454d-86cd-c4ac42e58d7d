package com.jd.qf.ai.server.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.biz.core.api.chat.ScrmAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.AsyncReplyCommand;
import com.jd.qf.ai.server.starter.Application;
import com.jdt.open.capability.ratelimiter.RateLimiterRule;
import com.jdt.open.capability.ratelimiter.RateLimiterRuleHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/5
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class RateLimitTest {

    @Autowired
    private ScrmAiChatService scrmAiChatService;
    @LafValue("open.rate.limit.rule")
    private String rateLimitRule;

    @Test
    public void testRateLimit() {
        for (int i = 0; i < 10; i++) {
            scrmAiChatService.asyncReply(new AsyncReplyCommand());
        }
    }
}
