server:
  port: 8011
  tomcat:
    max-threads: 600
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,text/javascript,application/javascript
#  context-path: /basic
pagehelper:
  helper-dialect: mysql
  reasonable: true

mybatis:
  configuration:
    map-underscore-to-camel-case: true

spring:
  profiles:
    active: "@profileActive@"
  application:
    name: qf-ai-server
  jackson:
    serialization: { WRITE_DATES_AS_TIMESTAMPS: true }
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true


# 发号器
identifier:
  cache:
    key:
      prefix: ${spring.application.name}

# open-lab
open:
  lab:
    business: qf-ai-server
    app: ${spring.application.name}
    api.packages: com.jd.qf.ai..*.api.*
    controller.packages: com.jd.qf.ai..*.facade.controller
  exception.handler:
    enable: false
    packages: ${open.lab.api.packages}
  sequence:
    enable: true
  log:
    enable: true
    packages: ${open.lab.api.packages},${open.lab.controller.packages}
  trace:
    enable: true
    packages: ${open.lab.api.packages},${open.lab.controller.packages}
  validator:
    enable: true
    packages: ${open.lab.api.packages},${open.lab.controller.packages}
  health:
    enable: true
  auto.cache:
    enable: true
    app: ${open.lab.app}
  event.flow:
    enable: false
    app: ${open.lab.app}
  jsf.log:
    max.length: 50000
  lock:
    enable: true
  identifier:
    enable: true
    key:
      prefix: ${spring.application.name}

#限流配置

  rate:
    limit:
      enable: true
      config: ducc
